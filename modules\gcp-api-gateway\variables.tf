# GCP API Gateway Variables

variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "region" {
  description = "GCP region"
  type        = string
  default     = "us-central1"
}

variable "enable_apis" {
  description = "Whether to enable required GCP APIs"
  type        = bool
  default     = true
}

variable "api_id" {
  description = "Identifier for the API Gateway API"
  type        = string
}

variable "display_name" {
  description = "Display name for the API"
  type        = string
}

variable "api_config_id" {
  description = "Identifier for the API config"
  type        = string
}

variable "gateway_id" {
  description = "Identifier for the gateway"
  type        = string
}

variable "gateway_display_name" {
  description = "Display name for the gateway"
  type        = string
  default     = null
}

variable "openapi_spec" {
  description = "OpenAPI specification for the API"
  type = object({
    path     = string
    contents = string
  })
}

variable "gateway_config" {
  description = "Gateway configuration"
  type = object({
    service_account = string
  })
  default = null
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}

variable "create_service_account" {
  description = "Whether to create a service account for the API Gateway"
  type        = bool
  default     = false
}

variable "service_account_id" {
  description = "ID for the service account"
  type        = string
  default     = "api-gateway-sa"
}

variable "service_account_display_name" {
  description = "Display name for the service account"
  type        = string
  default     = "API Gateway Service Account"
}

variable "existing_service_account" {
  description = "Email of existing service account to use"
  type        = string
  default     = null
}

variable "additional_iam_roles" {
  description = "Additional IAM roles to assign"
  type = map(object({
    role   = string
    member = string
  }))
  default = {}
}

variable "create_endpoints_service" {
  description = "Whether to create a Cloud Endpoints service"
  type        = bool
  default     = false
}

variable "endpoints_service_name" {
  description = "Name for the Cloud Endpoints service"
  type        = string
  default     = ""
}

variable "create_sample_backend" {
  description = "Whether to create a sample Cloud Run backend service"
  type        = bool
  default     = false
}

variable "backend_service_name" {
  description = "Name for the backend Cloud Run service"
  type        = string
  default     = "api-backend"
}

variable "backend_image" {
  description = "Container image for the backend service"
  type        = string
  default     = "gcr.io/cloudrun/hello"
}

variable "backend_env_vars" {
  description = "Environment variables for the backend service"
  type        = map(string)
  default     = {}
}

variable "security_policy" {
  description = "Security policy configuration"
  type = object({
    cors = optional(object({
      allow_origins      = list(string)
      allow_methods      = list(string)
      allow_headers      = list(string)
      expose_headers     = list(string)
      max_age           = number
      allow_credentials = bool
    }))
    
    authentication = optional(object({
      providers = list(object({
        id                = string
        issuer           = string
        jwks_uri         = string
        audiences        = list(string)
        authorization_url = optional(string)
      }))
    }))
    
    quota = optional(object({
      limit    = number
      interval = string
      unit     = string
    }))
  })
  default = null
}

# AWS API Gateway Module
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# API Gateway REST API
resource "aws_api_gateway_rest_api" "this" {
  name        = var.api_name
  description = var.api_description

  endpoint_configuration {
    types = var.endpoint_types
  }

  binary_media_types = var.binary_media_types

  tags = merge(
    var.tags,
    {
      Name = var.api_name
    }
  )
}

# API Gateway Deployment
resource "aws_api_gateway_deployment" "this" {
  count = var.create_deployment ? 1 : 0

  rest_api_id = aws_api_gateway_rest_api.this.id
  stage_name  = var.stage_name

  depends_on = [
    aws_api_gateway_method.this,
    aws_api_gateway_integration.this
  ]

  lifecycle {
    create_before_destroy = true
  }

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.this,
      aws_api_gateway_method.this,
      aws_api_gateway_integration.this,
    ]))
  }
}

# API Gateway Stage
resource "aws_api_gateway_stage" "this" {
  count = var.create_deployment ? 1 : 0

  deployment_id = aws_api_gateway_deployment.this[0].id
  rest_api_id   = aws_api_gateway_rest_api.this.id
  stage_name    = var.stage_name

  access_log_settings {
    destination_arn = var.cloudwatch_log_group_arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      caller         = "$context.identity.caller"
      user           = "$context.identity.user"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      resourcePath   = "$context.resourcePath"
      status         = "$context.status"
      protocol       = "$context.protocol"
      responseLength = "$context.responseLength"
    })
  }

  xray_tracing_enabled = var.xray_tracing_enabled

  tags = var.tags
}

# API Gateway Resources
resource "aws_api_gateway_resource" "this" {
  for_each = var.resources

  rest_api_id = aws_api_gateway_rest_api.this.id
  parent_id   = each.value.parent_id != null ? each.value.parent_id : aws_api_gateway_rest_api.this.root_resource_id
  path_part   = each.value.path_part
}

# API Gateway Methods
resource "aws_api_gateway_method" "this" {
  for_each = var.methods

  rest_api_id   = aws_api_gateway_rest_api.this.id
  resource_id   = aws_api_gateway_resource.this[each.value.resource_key].id
  http_method   = each.value.http_method
  authorization = each.value.authorization

  authorizer_id = each.value.authorizer_id
  api_key_required = each.value.api_key_required

  request_parameters = each.value.request_parameters
  request_models     = each.value.request_models
}

# API Gateway Integrations
resource "aws_api_gateway_integration" "this" {
  for_each = var.methods

  rest_api_id = aws_api_gateway_rest_api.this.id
  resource_id = aws_api_gateway_resource.this[each.value.resource_key].id
  http_method = aws_api_gateway_method.this[each.key].http_method

  integration_http_method = each.value.integration.http_method
  type                   = each.value.integration.type
  uri                    = each.value.integration.uri

  request_parameters = each.value.integration.request_parameters
  request_templates  = each.value.integration.request_templates

  timeout_milliseconds = each.value.integration.timeout_milliseconds
}

# API Gateway Method Responses
resource "aws_api_gateway_method_response" "this" {
  for_each = local.method_responses

  rest_api_id = aws_api_gateway_rest_api.this.id
  resource_id = aws_api_gateway_resource.this[each.value.resource_key].id
  http_method = aws_api_gateway_method.this[each.value.method_key].http_method
  status_code = each.value.status_code

  response_models = each.value.response_models
  response_parameters = each.value.response_parameters
}

# API Gateway Integration Responses
resource "aws_api_gateway_integration_response" "this" {
  for_each = local.method_responses

  rest_api_id = aws_api_gateway_rest_api.this.id
  resource_id = aws_api_gateway_resource.this[each.value.resource_key].id
  http_method = aws_api_gateway_method.this[each.value.method_key].http_method
  status_code = aws_api_gateway_method_response.this[each.key].status_code

  response_parameters = each.value.integration_response_parameters
  response_templates  = each.value.integration_response_templates

  depends_on = [aws_api_gateway_integration.this]
}

# Local values for processing method responses
locals {
  method_responses = merge([
    for method_key, method in var.methods : {
      for response_key, response in method.responses : "${method_key}-${response_key}" => merge(response, {
        method_key   = method_key
        resource_key = method.resource_key
      })
    }
  ]...)
}

# API Key
resource "aws_api_gateway_api_key" "this" {
  count = var.create_api_key ? 1 : 0

  name        = var.api_key_name
  description = var.api_key_description

  tags = var.tags
}

# Usage Plan
resource "aws_api_gateway_usage_plan" "this" {
  count = var.create_usage_plan ? 1 : 0

  name        = var.usage_plan_name
  description = var.usage_plan_description

  api_stages {
    api_id = aws_api_gateway_rest_api.this.id
    stage  = var.create_deployment ? aws_api_gateway_stage.this[0].stage_name : var.stage_name
  }

  throttle_settings {
    rate_limit  = var.throttle_settings.rate_limit
    burst_limit = var.throttle_settings.burst_limit
  }

  quota_settings {
    limit  = var.quota_settings.limit
    period = var.quota_settings.period
  }

  tags = var.tags
}

# Usage Plan Key
resource "aws_api_gateway_usage_plan_key" "this" {
  count = var.create_api_key && var.create_usage_plan ? 1 : 0

  key_id        = aws_api_gateway_api_key.this[0].id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.this[0].id
}

# Custom Domain Name
resource "aws_api_gateway_domain_name" "this" {
  count = var.create_domain_name ? 1 : 0

  domain_name              = var.domain_name
  regional_certificate_arn = var.certificate_arn
  security_policy         = var.security_policy

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = var.tags
}

# Base Path Mapping
resource "aws_api_gateway_base_path_mapping" "this" {
  count = var.create_domain_name ? 1 : 0

  api_id      = aws_api_gateway_rest_api.this.id
  stage_name  = var.create_deployment ? aws_api_gateway_stage.this[0].stage_name : var.stage_name
  domain_name = aws_api_gateway_domain_name.this[0].domain_name
}

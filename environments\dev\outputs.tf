# Development Environment Outputs

output "api_gateway_url" {
  description = "URL of the development API Gateway"
  value       = module.dev_api_gateway.invoke_url
}

output "api_gateway_id" {
  description = "ID of the development API Gateway"
  value       = module.dev_api_gateway.api_id
}

output "api_gateway_arn" {
  description = "ARN of the development API Gateway"
  value       = module.dev_api_gateway.api_arn
}

output "health_check_url" {
  description = "Health check endpoint URL"
  value       = "${module.dev_api_gateway.invoke_url}/health"
}

output "api_v1_url" {
  description = "API v1 base URL"
  value       = "${module.dev_api_gateway.invoke_url}/api/v1"
}

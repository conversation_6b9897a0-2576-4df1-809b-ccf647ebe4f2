variable "vpc_link_id" {
  description = "The ID of the existing VPC Link"
  type        = string
}

variable "backend_service_url" {
  description = "The backend service URL for integration"
  type        = string
  default     = "http://internal-backend.local"
}

variable "api_name" {
  description = "Name of the API Gateway"
  type        = string
  default     = "humanai-api"
}

variable "stage_name" {
  description = "Stage name for deployment"
  type        = string
  default     = "v1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}
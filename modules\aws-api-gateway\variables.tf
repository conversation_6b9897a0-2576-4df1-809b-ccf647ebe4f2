# API Gateway Variables

variable "api_name" {
  description = "Name of the API Gateway"
  type        = string
}

variable "api_description" {
  description = "Description of the API Gateway"
  type        = string
  default     = ""
}

variable "endpoint_types" {
  description = "List of endpoint types. Valid values: EDGE, REGIONAL, PRIVATE"
  type        = list(string)
  default     = ["REGIONAL"]
}

variable "binary_media_types" {
  description = "List of binary media types supported by the REST API"
  type        = list(string)
  default     = []
}

variable "policy" {
  description = "JSON formatted policy document for the API Gateway"
  type        = string
  default     = null
}

variable "create_deployment" {
  description = "Whether to create a deployment"
  type        = bool
  default     = true
}

variable "stage_name" {
  description = "Name of the stage"
  type        = string
  default     = "dev"
}

variable "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for access logs"
  type        = string
  default     = null
}

variable "xray_tracing_enabled" {
  description = "Whether to enable X-Ray tracing"
  type        = bool
  default     = false
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
}

variable "resources" {
  description = "Map of API Gateway resources to create"
  type = map(object({
    path_part = string
    parent_id = optional(string)
  }))
  default = {}
}

variable "methods" {
  description = "Map of API Gateway methods to create"
  type = map(object({
    resource_key     = string
    http_method      = string
    authorization    = string
    authorizer_id    = optional(string)
    api_key_required = optional(bool, false)
    request_parameters = optional(map(bool), {})
    request_models     = optional(map(string), {})
    
    integration = object({
      type                    = string
      http_method            = optional(string)
      uri                    = optional(string)
      request_parameters     = optional(map(string), {})
      request_templates      = optional(map(string), {})
      timeout_milliseconds   = optional(number, 29000)
    })
    
    responses = optional(map(object({
      status_code = string
      response_models = optional(map(string), {})
      response_parameters = optional(map(bool), {})
      integration_response_parameters = optional(map(string), {})
      integration_response_templates = optional(map(string), {})
    })), {
      "200" = {
        status_code = "200"
        response_models = {
          "application/json" = "Empty"
        }
      }
    })
  }))
  default = {}
}

# API Key variables
variable "create_api_key" {
  description = "Whether to create an API key"
  type        = bool
  default     = false
}

variable "api_key_name" {
  description = "Name of the API key"
  type        = string
  default     = ""
}

variable "api_key_description" {
  description = "Description of the API key"
  type        = string
  default     = ""
}

# Usage Plan variables
variable "create_usage_plan" {
  description = "Whether to create a usage plan"
  type        = bool
  default     = false
}

variable "usage_plan_name" {
  description = "Name of the usage plan"
  type        = string
  default     = ""
}

variable "usage_plan_description" {
  description = "Description of the usage plan"
  type        = string
  default     = ""
}

variable "throttle_settings" {
  description = "Throttle settings for the usage plan"
  type = object({
    rate_limit  = number
    burst_limit = number
  })
  default = {
    rate_limit  = 10000
    burst_limit = 5000
  }
}

variable "quota_settings" {
  description = "Quota settings for the usage plan"
  type = object({
    limit  = number
    period = string
  })
  default = {
    limit  = 1000000
    period = "MONTH"
  }
}

# Domain variables
variable "create_domain_name" {
  description = "Whether to create a custom domain name"
  type        = bool
  default     = false
}

variable "domain_name" {
  description = "Custom domain name"
  type        = string
  default     = ""
}

variable "certificate_arn" {
  description = "ARN of the certificate for the domain"
  type        = string
  default     = ""
}

variable "security_policy" {
  description = "Security policy for the domain name"
  type        = string
  default     = "TLS_1_2"
}

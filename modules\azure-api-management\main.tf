# Azure API Management Module
terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

# Resource Group (optional - can be passed as variable)
resource "azurerm_resource_group" "this" {
  count = var.create_resource_group ? 1 : 0

  name     = var.resource_group_name
  location = var.location

  tags = var.tags
}

# API Management Service
resource "azurerm_api_management" "this" {
  name                = var.api_management_name
  location            = var.location
  resource_group_name = var.create_resource_group ? azurerm_resource_group.this[0].name : var.resource_group_name
  publisher_name      = var.publisher_name
  publisher_email     = var.publisher_email

  sku_name = var.sku_name

  identity {
    type = var.identity_type
  }

  dynamic "additional_location" {
    for_each = var.additional_locations
    content {
      location = additional_location.value.location
      capacity = additional_location.value.capacity
    }
  }

  tags = var.tags
}

# API Management APIs
resource "azurerm_api_management_api" "this" {
  for_each = var.apis

  name                = each.key
  resource_group_name = azurerm_api_management.this.resource_group_name
  api_management_name = azurerm_api_management.this.name
  revision            = each.value.revision
  display_name        = each.value.display_name
  path                = each.value.path
  protocols           = each.value.protocols

  description                = each.value.description
  service_url               = each.value.service_url
  subscription_required     = each.value.subscription_required
  subscription_key_parameter_names {
    header = each.value.subscription_key_header_name
    query  = each.value.subscription_key_query_name
  }

  dynamic "import" {
    for_each = each.value.import_config != null ? [each.value.import_config] : []
    content {
      content_format = import.value.content_format
      content_value  = import.value.content_value
    }
  }
}

# API Management Operations
resource "azurerm_api_management_api_operation" "this" {
  for_each = local.operations

  operation_id        = each.key
  api_name           = each.value.api_name
  api_management_name = azurerm_api_management.this.name
  resource_group_name = azurerm_api_management.this.resource_group_name
  display_name       = each.value.display_name
  method             = each.value.method
  url_template       = each.value.url_template
  description        = each.value.description

  dynamic "request" {
    for_each = each.value.request != null ? [each.value.request] : []
    content {
      description = request.value.description
      
      dynamic "header" {
        for_each = request.value.headers
        content {
          name        = header.value.name
          required    = header.value.required
          type        = header.value.type
          description = header.value.description
        }
      }

      dynamic "query_parameter" {
        for_each = request.value.query_parameters
        content {
          name        = query_parameter.value.name
          required    = query_parameter.value.required
          type        = query_parameter.value.type
          description = query_parameter.value.description
        }
      }
    }
  }

  dynamic "response" {
    for_each = each.value.responses
    content {
      status_code = response.value.status_code
      description = response.value.description
      
      dynamic "header" {
        for_each = response.value.headers
        content {
          name        = header.value.name
          required    = header.value.required
          type        = header.value.type
          description = header.value.description
        }
      }
    }
  }
}

# API Management Products
resource "azurerm_api_management_product" "this" {
  for_each = var.products

  product_id          = each.key
  api_management_name = azurerm_api_management.this.name
  resource_group_name = azurerm_api_management.this.resource_group_name
  display_name        = each.value.display_name
  subscription_required = each.value.subscription_required
  approval_required   = each.value.approval_required
  published           = each.value.published
  description         = each.value.description
  terms               = each.value.terms
}

# API Management Product APIs
resource "azurerm_api_management_product_api" "this" {
  for_each = local.product_apis

  api_name            = each.value.api_name
  product_id          = each.value.product_id
  api_management_name = azurerm_api_management.this.name
  resource_group_name = azurerm_api_management.this.resource_group_name

  depends_on = [
    azurerm_api_management_api.this,
    azurerm_api_management_product.this
  ]
}

# Local values for processing operations and product APIs
locals {
  operations = merge([
    for api_key, api in var.apis : {
      for op_key, operation in api.operations : "${api_key}-${op_key}" => merge(operation, {
        api_name = api_key
      })
    }
  ]...)

  product_apis = merge([
    for product_key, product in var.products : {
      for api_name in product.api_names : "${product_key}-${api_name}" => {
        product_id = product_key
        api_name   = api_name
      }
    }
  ]...)
}

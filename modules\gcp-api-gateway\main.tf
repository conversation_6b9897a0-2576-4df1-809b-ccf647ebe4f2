# GCP API Gateway Module
terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
}

# Enable required APIs
resource "google_project_service" "apigateway" {
  count   = var.enable_apis ? 1 : 0
  service = "apigateway.googleapis.com"
  project = var.project_id

  disable_dependent_services = false
  disable_on_destroy         = false
}

resource "google_project_service" "servicecontrol" {
  count   = var.enable_apis ? 1 : 0
  service = "servicecontrol.googleapis.com"
  project = var.project_id

  disable_dependent_services = false
  disable_on_destroy         = false
}

resource "google_project_service" "servicemanagement" {
  count   = var.enable_apis ? 1 : 0
  service = "servicemanagement.googleapis.com"
  project = var.project_id

  disable_dependent_services = false
  disable_on_destroy         = false
}

# API Gateway API
resource "google_api_gateway_api" "this" {
  provider = google

  api_id       = var.api_id
  display_name = var.display_name
  project      = var.project_id

  labels = var.labels

  depends_on = [
    google_project_service.apigateway,
    google_project_service.servicecontrol,
    google_project_service.servicemanagement
  ]
}

# API Gateway API Config
resource "google_api_gateway_api_config" "this" {
  provider = google

  api         = google_api_gateway_api.this.api_id
  api_config_id = var.api_config_id
  project     = var.project_id

  openapi_documents {
    document {
      path     = var.openapi_spec.path
      contents = base64encode(var.openapi_spec.contents)
    }
  }

  dynamic "gateway_config" {
    for_each = var.gateway_config != null ? [var.gateway_config] : []
    content {
      backend_config {
        google_service_account = gateway_config.value.service_account
      }
    }
  }

  labels = var.labels

  lifecycle {
    create_before_destroy = true
  }
}

# API Gateway Gateway
resource "google_api_gateway_gateway" "this" {
  provider = google

  api_config = google_api_gateway_api_config.this.id
  gateway_id = var.gateway_id
  region     = var.region
  project    = var.project_id

  display_name = var.gateway_display_name

  labels = var.labels

  depends_on = [google_api_gateway_api_config.this]
}

# Service Account for API Gateway (optional)
resource "google_service_account" "api_gateway" {
  count = var.create_service_account ? 1 : 0

  account_id   = var.service_account_id
  display_name = var.service_account_display_name
  description  = "Service account for API Gateway ${var.api_id}"
  project      = var.project_id
}

# IAM binding for service account
resource "google_project_iam_member" "api_gateway_invoker" {
  count = var.create_service_account ? 1 : 0

  project = var.project_id
  role    = "roles/run.invoker"
  member  = "serviceAccount:${google_service_account.api_gateway[0].email}"
}

# Additional IAM bindings
resource "google_project_iam_member" "additional" {
  for_each = var.additional_iam_roles

  project = var.project_id
  role    = each.value.role
  member  = each.value.member
}

# Cloud Endpoints Service (if using Cloud Endpoints)
resource "google_endpoints_service" "this" {
  count = var.create_endpoints_service ? 1 : 0

  service_name   = var.endpoints_service_name
  project        = var.project_id
  openapi_config = var.openapi_spec.contents

  depends_on = [
    google_project_service.servicemanagement
  ]
}

# Cloud Run service (example backend)
resource "google_cloud_run_service" "backend" {
  count = var.create_sample_backend ? 1 : 0

  name     = var.backend_service_name
  location = var.region
  project  = var.project_id

  template {
    spec {
      containers {
        image = var.backend_image
        
        dynamic "env" {
          for_each = var.backend_env_vars
          content {
            name  = env.key
            value = env.value
          }
        }
      }
      
      service_account_name = var.create_service_account ? google_service_account.api_gateway[0].email : var.existing_service_account
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  depends_on = [google_project_service.apigateway]
}

# IAM policy for Cloud Run service
resource "google_cloud_run_service_iam_member" "invoker" {
  count = var.create_sample_backend ? 1 : 0

  service  = google_cloud_run_service.backend[0].name
  location = google_cloud_run_service.backend[0].location
  project  = var.project_id
  role     = "roles/run.invoker"
  member   = var.create_service_account ? "serviceAccount:${google_service_account.api_gateway[0].email}" : "serviceAccount:${var.existing_service_account}"
}

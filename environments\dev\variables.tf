# Development Environment Variables

variable "aws_region" {
  description = "AWS region for development environment"
  type        = string
  default     = "us-east-1"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "api_resources" {
  description = "API Gateway resources configuration"
  type = map(object({
    path_part = string
    parent_id = optional(string)
  }))
  default = {}
}

variable "api_methods" {
  description = "API Gateway methods configuration"
  type = map(object({
    resource_key     = string
    http_method      = string
    authorization    = string
    authorizer_id    = optional(string)
    api_key_required = optional(bool, false)
    request_parameters = optional(map(bool), {})
    request_models     = optional(map(string), {})
    
    integration = object({
      type                    = string
      http_method            = optional(string)
      uri                    = optional(string)
      request_parameters     = optional(map(string), {})
      request_templates      = optional(map(string), {})
      timeout_milliseconds   = optional(number, 29000)
    })
    
    responses = optional(map(object({
      status_code = string
      response_models = optional(map(string), {})
      response_parameters = optional(map(bool), {})
      integration_response_parameters = optional(map(string), {})
      integration_response_templates = optional(map(string), {})
    })), {
      "200" = {
        status_code = "200"
        response_models = {
          "application/json" = "Empty"
        }
      }
    })
  }))
  default = {}
}

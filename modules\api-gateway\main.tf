resource "aws_api_gateway_rest_api" "humanai_api" {
  name        = var.api_name
  description = "HumanAI API Gateway for text-to-speech, streaming, and session management"
  
  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Name        = var.api_name
    Environment = var.environment
  }
}


# Resource: /ping
resource "aws_api_gateway_resource" "ping" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_rest_api.humanai_api.root_resource_id
  path_part   = "ping"
}

# Resource: /text2speech
resource "aws_api_gateway_resource" "text2speech" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_rest_api.humanai_api.root_resource_id
  path_part   = "text2speech"
}

# Resource: /text2speech/pause_audio
resource "aws_api_gateway_resource" "text2speech_pause_audio" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech.id
  path_part   = "pause_audio"
}

# Resource: /text2speech/clear_audio
resource "aws_api_gateway_resource" "text2speech_clear_audio" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech.id
  path_part   = "clear_audio"
}

# Resource: /text2speech/send_message
resource "aws_api_gateway_resource" "text2speech_send_message" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech.id
  path_part   = "send_message"
}

# Resource: /text2speech/show_subtitle
resource "aws_api_gateway_resource" "text2speech_show_subtitle" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech.id
  path_part   = "show_subtitle"
}

# Resource: /text2speech/edgetts
resource "aws_api_gateway_resource" "text2speech_edgetts" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech.id
  path_part   = "edgetts"
}

# Resource: /text2speech/edgetts/set_voice_name
resource "aws_api_gateway_resource" "text2speech_edgetts_set_voice_name" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech_edgetts.id
  path_part   = "set_voice_name"
}

# Resource: /text2speech/edgetts/get_voice_names
resource "aws_api_gateway_resource" "text2speech_edgetts_get_voice_names" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech_edgetts.id
  path_part   = "get_voice_names"
}

# Resource: /text2speech/edgetts/get_audio_stream
resource "aws_api_gateway_resource" "text2speech_edgetts_get_audio_stream" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.text2speech_edgetts.id
  path_part   = "get_audio_stream"
}

# Resource: /stream
resource "aws_api_gateway_resource" "stream" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_rest_api.humanai_api.root_resource_id
  path_part   = "stream"
}

# Resource: /session
resource "aws_api_gateway_resource" "session" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_rest_api.humanai_api.root_resource_id
  path_part   = "session"
}

# Resource: /session/start_session
resource "aws_api_gateway_resource" "session_start_session" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.session.id
  path_part   = "start_session"
}

# Resource: /session/clear_session
resource "aws_api_gateway_resource" "session_clear_session" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.session.id
  path_part   = "clear_session"
}

# Resource: /session/get_models
resource "aws_api_gateway_resource" "session_get_models" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.session.id
  path_part   = "get_models"
}

# Resource: /session/get_stream_mode
resource "aws_api_gateway_resource" "session_get_stream_mode" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  parent_id   = aws_api_gateway_resource.session.id
  path_part   = "get_stream_mode"
}

# Method and Integration: GET /ping
resource "aws_api_gateway_method" "ping_get" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.ping.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "ping_get_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.ping.id
  http_method = aws_api_gateway_method.ping_get.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "GET"
  uri                     = "${var.backend_service_url}/ping"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /text2speech/pause_audio
resource "aws_api_gateway_method" "text2speech_pause_audio_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_pause_audio.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_pause_audio_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_pause_audio.id
  http_method = aws_api_gateway_method.text2speech_pause_audio_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/text2speech/pause_audio"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /text2speech/clear_audio
resource "aws_api_gateway_method" "text2speech_clear_audio_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_clear_audio.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_clear_audio_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_clear_audio.id
  http_method = aws_api_gateway_method.text2speech_clear_audio_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/text2speech/clear_audio"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /text2speech/send_message
resource "aws_api_gateway_method" "text2speech_send_message_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_send_message.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_send_message_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_send_message.id
  http_method = aws_api_gateway_method.text2speech_send_message_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/text2speech/send_message"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /text2speech/show_subtitle
resource "aws_api_gateway_method" "text2speech_show_subtitle_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_show_subtitle.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_show_subtitle_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_show_subtitle.id
  http_method = aws_api_gateway_method.text2speech_show_subtitle_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/text2speech/show_subtitle"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /text2speech/edgetts/set_voice_name
resource "aws_api_gateway_method" "text2speech_edgetts_set_voice_name_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_edgetts_set_voice_name.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_edgetts_set_voice_name_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_edgetts_set_voice_name.id
  http_method = aws_api_gateway_method.text2speech_edgetts_set_voice_name_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/text2speech/edgetts/set_voice_name"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: GET /text2speech/edgetts/get_voice_names
resource "aws_api_gateway_method" "text2speech_edgetts_get_voice_names_get" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_edgetts_get_voice_names.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_edgetts_get_voice_names_get_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_edgetts_get_voice_names.id
  http_method = aws_api_gateway_method.text2speech_edgetts_get_voice_names_get.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "GET"
  uri                     = "${var.backend_service_url}/text2speech/edgetts/get_voice_names"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: GET /text2speech/edgetts/get_audio_stream
resource "aws_api_gateway_method" "text2speech_edgetts_get_audio_stream_get" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.text2speech_edgetts_get_audio_stream.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "text2speech_edgetts_get_audio_stream_get_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.text2speech_edgetts_get_audio_stream.id
  http_method = aws_api_gateway_method.text2speech_edgetts_get_audio_stream_get.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "GET"
  uri                     = "${var.backend_service_url}/text2speech/edgetts/get_audio_stream"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: GET /stream/
resource "aws_api_gateway_method" "stream_get" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.stream.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "stream_get_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.stream.id
  http_method = aws_api_gateway_method.stream_get.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "GET"
  uri                     = "${var.backend_service_url}/stream/"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /session/start_session
resource "aws_api_gateway_method" "session_start_session_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.session_start_session.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "session_start_session_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.session_start_session.id
  http_method = aws_api_gateway_method.session_start_session_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/session/start_session"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: POST /session/clear_session
resource "aws_api_gateway_method" "session_clear_session_post" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.session_clear_session.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "session_clear_session_post_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.session_clear_session.id
  http_method = aws_api_gateway_method.session_clear_session_post.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "POST"
  uri                     = "${var.backend_service_url}/session/clear_session"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: GET /session/get_models
resource "aws_api_gateway_method" "session_get_models_get" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.session_get_models.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "session_get_models_get_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.session_get_models.id
  http_method = aws_api_gateway_method.session_get_models_get.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "GET"
  uri                     = "${var.backend_service_url}/session/get_models"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# Method and Integration: GET /session/get_stream_mode
resource "aws_api_gateway_method" "session_get_stream_mode_get" {
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  resource_id   = aws_api_gateway_resource.session_get_stream_mode.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "session_get_stream_mode_get_integration" {
  rest_api_id = aws_api_gateway_rest_api.humanai_api.id
  resource_id = aws_api_gateway_resource.session_get_stream_mode.id
  http_method = aws_api_gateway_method.session_get_stream_mode_get.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "GET"
  uri                     = "${var.backend_service_url}/session/get_stream_mode"
  connection_type         = "VPC_LINK"
  connection_id           = var.vpc_link_id
}

# API Gateway Deployment
resource "aws_api_gateway_deployment" "humanai_api_deployment" {
  depends_on = [
    aws_api_gateway_integration.ping_get_integration,
    aws_api_gateway_integration.text2speech_pause_audio_post_integration,
    aws_api_gateway_integration.text2speech_clear_audio_post_integration,
    aws_api_gateway_integration.text2speech_send_message_post_integration,
    aws_api_gateway_integration.text2speech_show_subtitle_post_integration,
    aws_api_gateway_integration.text2speech_edgetts_set_voice_name_post_integration,
    aws_api_gateway_integration.text2speech_edgetts_get_voice_names_get_integration,
    aws_api_gateway_integration.text2speech_edgetts_get_audio_stream_get_integration,
    aws_api_gateway_integration.stream_get_integration,
    aws_api_gateway_integration.session_start_session_post_integration,
    aws_api_gateway_integration.session_clear_session_post_integration,
    aws_api_gateway_integration.session_get_models_get_integration,
    aws_api_gateway_integration.session_get_stream_mode_get_integration,
  ]

  rest_api_id = aws_api_gateway_rest_api.humanai_api.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.ping.id,
      aws_api_gateway_resource.text2speech.id,
      aws_api_gateway_resource.stream.id,
      aws_api_gateway_resource.session.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

# API Gateway Stage
resource "aws_api_gateway_stage" "humanai_api_stage" {
  deployment_id = aws_api_gateway_deployment.humanai_api_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.humanai_api.id
  stage_name    = var.stage_name

  tags = {
    Name        = "${var.api_name}-${var.stage_name}"
    Environment = var.environment
  }
}
# API Gateway Terraform Modules

This repository contains Terraform modules for creating and managing API Gateways across different cloud providers.

## Structure

```
.
├── modules/                    # Reusable Terraform modules
│   ├── aws-api-gateway/       # AWS API Gateway module
│   ├── azure-api-management/  # Azure API Management module
│   └── gcp-api-gateway/       # GCP API Gateway module
├── environments/              # Environment-specific configurations
│   ├── dev/                   # Development environment
│   ├── staging/               # Staging environment
│   └── prod/                  # Production environment
├── examples/                  # Usage examples
├── scripts/                   # Helper scripts
└── docs/                      # Documentation
```

## Usage

1. Choose the appropriate module from the `modules/` directory
2. Reference it in your environment configuration
3. Customize variables as needed
4. Apply with Terraform

## Getting Started

See the examples in the `examples/` directory for common usage patterns.

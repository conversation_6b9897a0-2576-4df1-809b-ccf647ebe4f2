# Azure API Management Variables

variable "api_management_name" {
  description = "Name of the API Management service"
  type        = string
}

variable "location" {
  description = "Azure region where resources will be created"
  type        = string
}

variable "create_resource_group" {
  description = "Whether to create a new resource group"
  type        = bool
  default     = true
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "publisher_name" {
  description = "Publisher name for the API Management service"
  type        = string
}

variable "publisher_email" {
  description = "Publisher email for the API Management service"
  type        = string
}

variable "sku_name" {
  description = "SKU name for the API Management service"
  type        = string
  default     = "Developer_1"
  validation {
    condition = contains([
      "Developer_1", "Basic_1", "Basic_2", "Standard_1", "Standard_2",
      "Premium_1", "Premium_2", "Premium_4", "Premium_8", "Premium_12",
      "Consumption_0"
    ], var.sku_name)
    error_message = "SKU name must be a valid API Management SKU."
  }
}

variable "identity_type" {
  description = "Type of managed identity"
  type        = string
  default     = "SystemAssigned"
}

variable "additional_locations" {
  description = "Additional locations for the API Management service"
  type = list(object({
    location = string
    capacity = number
  }))
  default = []
}

variable "tags" {
  description = "A map of tags to assign to the resources"
  type        = map(string)
  default     = {}
}

variable "apis" {
  description = "Map of APIs to create"
  type = map(object({
    revision                        = string
    display_name                   = string
    path                          = string
    protocols                     = list(string)
    description                   = optional(string, "")
    service_url                   = optional(string)
    subscription_required         = optional(bool, true)
    subscription_key_header_name  = optional(string, "Ocp-Apim-Subscription-Key")
    subscription_key_query_name   = optional(string, "subscription-key")
    
    import_config = optional(object({
      content_format = string
      content_value  = string
    }))
    
    operations = optional(map(object({
      display_name  = string
      method        = string
      url_template  = string
      description   = optional(string, "")
      
      request = optional(object({
        description = optional(string, "")
        headers = optional(list(object({
          name        = string
          required    = bool
          type        = string
          description = optional(string, "")
        })), [])
        query_parameters = optional(list(object({
          name        = string
          required    = bool
          type        = string
          description = optional(string, "")
        })), [])
      }))
      
      responses = optional(list(object({
        status_code = number
        description = optional(string, "")
        headers = optional(list(object({
          name        = string
          required    = bool
          type        = string
          description = optional(string, "")
        })), [])
      })), [])
    })), {})
  }))
  default = {}
}

variable "products" {
  description = "Map of products to create"
  type = map(object({
    display_name          = string
    subscription_required = optional(bool, true)
    approval_required     = optional(bool, false)
    published            = optional(bool, true)
    description          = optional(string, "")
    terms                = optional(string)
    api_names            = optional(list(string), [])
  }))
  default = {}
}

variable "subscriptions" {
  description = "Map of subscriptions to create"
  type = map(object({
    display_name = string
    product_id   = optional(string)
    user_id      = optional(string)
    state        = optional(string, "active")
  }))
  default = {}
}

variable "policies" {
  description = "Map of policies to apply"
  type = map(object({
    level       = string # "global", "product", "api", "operation"
    target_id   = optional(string)
    xml_content = string
  }))
  default = {}
}

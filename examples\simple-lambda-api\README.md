# Simple Lambda API Example

This example demonstrates how to create a simple API Gateway that integrates with a Lambda function.

## Architecture

- **API Gateway**: REST API with `/hello` endpoint
- **Lambda Function**: Python function that returns a JSON response
- **CloudWatch**: Logging for API Gateway
- **IAM**: Roles and permissions for Lambda execution

## Endpoints

- `GET /hello` - Returns a hello message
- `POST /hello` - Returns a hello message with request details
- `OPTIONS /hello` - CORS preflight support

## Usage

1. Initialize Terraform:
   ```bash
   terraform init
   ```

2. Plan the deployment:
   ```bash
   terraform plan
   ```

3. Apply the configuration:
   ```bash
   terraform apply
   ```

4. Test the API:
   ```bash
   # Get the API URL from outputs
   API_URL=$(terraform output -raw api_gateway_url)
   
   # Test GET endpoint
   curl "$API_URL/hello"
   
   # Test POST endpoint
   curl -X POST "$API_URL/hello" -d '{"test": "data"}' -H "Content-Type: application/json"
   ```

## Cleanup

To destroy the resources:
```bash
terraform destroy
```

## Customization

You can customize the deployment by modifying the variables:

```bash
terraform apply -var="api_name=my-custom-api" -var="stage_name=prod"
```

## Expected Response

```json
{
  "message": "Hello from Lambda!",
  "path": "/hello",
  "method": "GET"
}
```

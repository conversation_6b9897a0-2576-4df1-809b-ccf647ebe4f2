# API Gateway Outputs

output "api_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.this.id
}

output "api_arn" {
  description = "ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.this.arn
}

output "api_name" {
  description = "Name of the API Gateway"
  value       = aws_api_gateway_rest_api.this.name
}

output "api_root_resource_id" {
  description = "Root resource ID of the API Gateway"
  value       = aws_api_gateway_rest_api.this.root_resource_id
}

output "api_execution_arn" {
  description = "Execution ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.this.execution_arn
}

output "deployment_id" {
  description = "ID of the deployment"
  value       = var.create_deployment ? aws_api_gateway_deployment.this[0].id : null
}

output "stage_name" {
  description = "Name of the stage"
  value       = var.create_deployment ? aws_api_gateway_stage.this[0].stage_name : null
}

output "stage_arn" {
  description = "ARN of the stage"
  value       = var.create_deployment ? aws_api_gateway_stage.this[0].arn : null
}

output "invoke_url" {
  description = "URL to invoke the API pointing to the stage"
  value       = var.create_deployment ? aws_api_gateway_stage.this[0].invoke_url : null
}

output "resource_ids" {
  description = "Map of resource names to their IDs"
  value = {
    for k, v in aws_api_gateway_resource.this : k => v.id
  }
}

output "method_ids" {
  description = "Map of method names to their resource and HTTP method"
  value = {
    for k, v in aws_api_gateway_method.this : k => {
      resource_id = v.resource_id
      http_method = v.http_method
    }
  }
}

output "api_key_id" {
  description = "ID of the API key"
  value       = var.create_api_key ? aws_api_gateway_api_key.this[0].id : null
}

output "api_key_value" {
  description = "Value of the API key"
  value       = var.create_api_key ? aws_api_gateway_api_key.this[0].value : null
  sensitive   = true
}

output "usage_plan_id" {
  description = "ID of the usage plan"
  value       = var.create_usage_plan ? aws_api_gateway_usage_plan.this[0].id : null
}

output "domain_name" {
  description = "Domain name"
  value       = var.create_domain_name ? aws_api_gateway_domain_name.this[0].domain_name : null
}

output "domain_name_cloudfront_domain_name" {
  description = "CloudFront domain name of the custom domain"
  value       = var.create_domain_name ? aws_api_gateway_domain_name.this[0].cloudfront_domain_name : null
}

output "domain_name_cloudfront_zone_id" {
  description = "CloudFront zone ID of the custom domain"
  value       = var.create_domain_name ? aws_api_gateway_domain_name.this[0].cloudfront_zone_id : null
}

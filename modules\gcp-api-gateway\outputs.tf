# GCP API Gateway Outputs

output "api_id" {
  description = "ID of the API Gateway API"
  value       = google_api_gateway_api.this.api_id
}

output "api_name" {
  description = "Name of the API Gateway API"
  value       = google_api_gateway_api.this.name
}

output "api_config_id" {
  description = "ID of the API config"
  value       = google_api_gateway_api_config.this.api_config_id
}

output "api_config_name" {
  description = "Name of the API config"
  value       = google_api_gateway_api_config.this.name
}

output "gateway_id" {
  description = "ID of the gateway"
  value       = google_api_gateway_gateway.this.gateway_id
}

output "gateway_name" {
  description = "Name of the gateway"
  value       = google_api_gateway_gateway.this.name
}

output "gateway_url" {
  description = "Default hostname of the gateway"
  value       = google_api_gateway_gateway.this.default_hostname
}

output "gateway_full_url" {
  description = "Full URL of the gateway"
  value       = "https://${google_api_gateway_gateway.this.default_hostname}"
}

output "service_account_email" {
  description = "Email of the created service account"
  value       = var.create_service_account ? google_service_account.api_gateway[0].email : null
}

output "service_account_id" {
  description = "ID of the created service account"
  value       = var.create_service_account ? google_service_account.api_gateway[0].id : null
}

output "endpoints_service_name" {
  description = "Name of the Cloud Endpoints service"
  value       = var.create_endpoints_service ? google_endpoints_service.this[0].service_name : null
}

output "endpoints_service_config_id" {
  description = "Config ID of the Cloud Endpoints service"
  value       = var.create_endpoints_service ? google_endpoints_service.this[0].config_id : null
}

output "backend_service_url" {
  description = "URL of the backend Cloud Run service"
  value       = var.create_sample_backend ? google_cloud_run_service.backend[0].status[0].url : null
}

output "backend_service_name" {
  description = "Name of the backend Cloud Run service"
  value       = var.create_sample_backend ? google_cloud_run_service.backend[0].name : null
}

output "project_id" {
  description = "GCP project ID"
  value       = var.project_id
}

output "region" {
  description = "GCP region"
  value       = var.region
}

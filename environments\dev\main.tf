# Development Environment Configuration
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Environment = "dev"
      ManagedBy   = "terraform"
      Project     = var.project_name
    }
  }
}

# Development API Gateway
module "dev_api_gateway" {
  source = "../../modules/aws-api-gateway"

  api_name        = "${var.project_name}-dev"
  api_description = "Development API Gateway for ${var.project_name}"
  stage_name      = "dev"

  endpoint_types           = ["REGIONAL"]
  xray_tracing_enabled     = true
  cloudwatch_log_group_arn = aws_cloudwatch_log_group.api_gateway.arn

  # Development-specific resources
  resources = var.api_resources

  # Development-specific methods
  methods = var.api_methods

  # No API key required for development
  create_api_key    = false
  create_usage_plan = false

  tags = {
    Environment = "dev"
    Project     = var.project_name
  }
}

# CloudWatch Log Group for API Gateway
resource "aws_cloudwatch_log_group" "api_gateway" {
  name              = "/aws/apigateway/${var.project_name}-dev"
  retention_in_days = 7

  tags = {
    Environment = "dev"
    Project     = var.project_name
  }
}

# Development Environment - API Gateway Configuration
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Environment = "dev"
      ManagedBy   = "terraform"
      Project     = var.project_name
    }
  }
}

# Development API Gateway
module "dev_api_gateway" {
  source = "../../modules/aws-api-gateway"

  api_name        = "${var.project_name}-dev"
  api_description = "Development API Gateway for ${var.project_name}"
  stage_name      = "dev"

  endpoint_types           = ["REGIONAL"]
  xray_tracing_enabled     = true
  cloudwatch_log_group_arn = aws_cloudwatch_log_group.api_gateway.arn

  # Development resources - simple structure
  resources = {
    api = {
      path_part = "api"
    }
    v1 = {
      path_part = "v1"
      parent_id = null # Will be resolved by module
    }
    health = {
      path_part = "health"
      parent_id = null # Will be resolved by module
    }
  }

  # Development methods - basic endpoints
  methods = {
    health_check = {
      resource_key  = "health"
      http_method   = "GET"
      authorization = "NONE"
      
      integration = {
        type = "MOCK"
        request_templates = {
          "application/json" = jsonencode({
            statusCode = 200
          })
        }
      }
      
      responses = {
        "200" = {
          status_code = "200"
          response_models = {
            "application/json" = "Empty"
          }
          integration_response_templates = {
            "application/json" = jsonencode({
              status = "healthy"
              environment = "dev"
              timestamp = "$context.requestTime"
            })
          }
        }
      }
    }
    
    options_cors = {
      resource_key  = "v1"
      http_method   = "OPTIONS"
      authorization = "NONE"
      
      integration = {
        type = "MOCK"
        request_templates = {
          "application/json" = jsonencode({
            statusCode = 200
          })
        }
      }
      
      responses = {
        "200" = {
          status_code = "200"
          response_parameters = {
            "method.response.header.Access-Control-Allow-Headers" = true
            "method.response.header.Access-Control-Allow-Methods" = true
            "method.response.header.Access-Control-Allow-Origin"  = true
          }
          integration_response_parameters = {
            "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
            "method.response.header.Access-Control-Allow-Methods" = "'GET,POST,PUT,DELETE,OPTIONS'"
            "method.response.header.Access-Control-Allow-Origin"  = "'*'"
          }
        }
      }
    }
  }

  # No API key or usage plan for development
  create_api_key    = false
  create_usage_plan = false

  tags = {
    Environment = "dev"
    Project     = var.project_name
  }
}

# CloudWatch Log Group for API Gateway
resource "aws_cloudwatch_log_group" "api_gateway" {
  name              = "/aws/apigateway/${var.project_name}-dev"
  retention_in_days = 7

  tags = {
    Environment = "dev"
    Project     = var.project_name
  }
}

# AWS API Gateway Terraform Module

This module creates an AWS API Gateway with configurable resources, methods, and integrations.

## Features

- ✅ REST API Gateway creation
- ✅ Custom resources and methods
- ✅ Lambda, HTTP, and AWS service integrations
- ✅ API key and usage plan management
- ✅ Custom domain name support
- ✅ CloudWatch logging and X-Ray tracing
- ✅ Flexible response configuration

## Usage

### Basic Example

```hcl
module "api_gateway" {
  source = "./modules/aws-api-gateway"

  api_name        = "my-api"
  api_description = "My REST API"
  stage_name      = "dev"

  resources = {
    users = {
      path_part = "users"
    }
    user_id = {
      path_part = "{id}"
      parent_id = module.api_gateway.resource_ids["users"]
    }
  }

  methods = {
    get_users = {
      resource_key  = "users"
      http_method   = "GET"
      authorization = "NONE"
      
      integration = {
        type        = "AWS_PROXY"
        http_method = "POST"
        uri         = "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/${aws_lambda_function.get_users.arn}/invocations"
      }
    }
  }

  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### Advanced Example with API Key

```hcl
module "api_gateway" {
  source = "./modules/aws-api-gateway"

  api_name        = "secure-api"
  api_description = "Secure API with rate limiting"
  stage_name      = "prod"

  # API Key configuration
  create_api_key      = true
  api_key_name        = "secure-api-key"
  api_key_description = "API key for secure API"

  # Usage Plan configuration
  create_usage_plan      = true
  usage_plan_name        = "secure-api-plan"
  usage_plan_description = "Usage plan for secure API"

  throttle_settings = {
    rate_limit  = 1000
    burst_limit = 500
  }

  quota_settings = {
    limit  = 100000
    period = "MONTH"
  }

  # Custom domain
  create_domain_name = true
  domain_name        = "api.example.com"
  certificate_arn    = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

  resources = {
    api = {
      path_part = "api"
    }
    v1 = {
      path_part = "v1"
      parent_id = module.api_gateway.resource_ids["api"]
    }
  }

  methods = {
    get_data = {
      resource_key     = "v1"
      http_method      = "GET"
      authorization    = "NONE"
      api_key_required = true
      
      integration = {
        type        = "HTTP"
        http_method = "GET"
        uri         = "https://backend.example.com/data"
      }
    }
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| api_name | Name of the API Gateway | `string` | n/a | yes |
| api_description | Description of the API Gateway | `string` | `""` | no |
| endpoint_types | List of endpoint types | `list(string)` | `["REGIONAL"]` | no |
| stage_name | Name of the stage | `string` | `"dev"` | no |
| resources | Map of API Gateway resources | `map(object)` | `{}` | no |
| methods | Map of API Gateway methods | `map(object)` | `{}` | no |
| create_api_key | Whether to create an API key | `bool` | `false` | no |
| create_usage_plan | Whether to create a usage plan | `bool` | `false` | no |
| create_domain_name | Whether to create a custom domain | `bool` | `false` | no |
| tags | A map of tags to assign | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| api_id | ID of the API Gateway |
| api_arn | ARN of the API Gateway |
| invoke_url | URL to invoke the API |
| api_key_value | Value of the API key (sensitive) |
| domain_name | Custom domain name |

## Integration Types

This module supports the following integration types:

- **AWS_PROXY**: Lambda proxy integration
- **AWS**: AWS service integration
- **HTTP**: HTTP integration
- **HTTP_PROXY**: HTTP proxy integration
- **MOCK**: Mock integration

## Authorization Types

- **NONE**: No authorization
- **AWS_IAM**: AWS IAM authorization
- **CUSTOM**: Custom authorizer
- **COGNITO_USER_POOLS**: Cognito User Pools

## Examples

See the `examples/` directory for more usage examples.

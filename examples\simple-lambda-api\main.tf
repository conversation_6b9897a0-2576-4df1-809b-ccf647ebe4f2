# Simple Lambda API Example
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Lambda function for the API
resource "aws_lambda_function" "hello_world" {
  filename         = "hello_world.zip"
  function_name    = "hello-world-api"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  tags = var.tags
}

# Lambda deployment package
data "archive_file" "lambda_zip" {
  type        = "zip"
  output_path = "hello_world.zip"
  source {
    content = <<EOF
import json

def handler(event, context):
    return {
        'statusCode': 200,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        'body': json.dumps({
            'message': 'Hello from Lambda!',
            'path': event.get('path', ''),
            'method': event.get('httpMethod', '')
        })
    }
EOF
    filename = "index.py"
  }
}

# IAM role for Lambda
resource "aws_iam_role" "lambda_role" {
  name = "hello-world-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy attachment for Lambda basic execution
resource "aws_iam_role_policy_attachment" "lambda_basic" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.lambda_role.name
}

# CloudWatch Log Group for API Gateway
resource "aws_cloudwatch_log_group" "api_gateway" {
  name              = "/aws/apigateway/${var.api_name}"
  retention_in_days = 7

  tags = var.tags
}

# API Gateway using the module
module "api_gateway" {
  source = "../../modules/aws-api-gateway"

  api_name        = var.api_name
  api_description = "Simple Lambda API example"
  stage_name      = var.stage_name

  cloudwatch_log_group_arn = aws_cloudwatch_log_group.api_gateway.arn
  xray_tracing_enabled     = true

  resources = {
    hello = {
      path_part = "hello"
    }
    users = {
      path_part = "users"
    }
    user_id = {
      path_part = "{id}"
      parent_id = null # Will be set after creation
    }
  }

  methods = {
    get_hello = {
      resource_key  = "hello"
      http_method   = "GET"
      authorization = "NONE"
      
      integration = {
        type        = "AWS_PROXY"
        http_method = "POST"
        uri         = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${aws_lambda_function.hello_world.arn}/invocations"
      }
    }
    
    post_hello = {
      resource_key  = "hello"
      http_method   = "POST"
      authorization = "NONE"
      
      integration = {
        type        = "AWS_PROXY"
        http_method = "POST"
        uri         = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${aws_lambda_function.hello_world.arn}/invocations"
      }
    }
    
    options_hello = {
      resource_key  = "hello"
      http_method   = "OPTIONS"
      authorization = "NONE"
      
      integration = {
        type = "MOCK"
        request_templates = {
          "application/json" = "{\"statusCode\": 200}"
        }
      }
      
      responses = {
        "200" = {
          status_code = "200"
          response_parameters = {
            "method.response.header.Access-Control-Allow-Headers" = true
            "method.response.header.Access-Control-Allow-Methods" = true
            "method.response.header.Access-Control-Allow-Origin"  = true
          }
          integration_response_parameters = {
            "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
            "method.response.header.Access-Control-Allow-Methods" = "'GET,POST,OPTIONS'"
            "method.response.header.Access-Control-Allow-Origin"  = "'*'"
          }
        }
      }
    }
  }

  tags = var.tags
}

# Lambda permission for API Gateway
resource "aws_lambda_permission" "api_gateway" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.hello_world.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${module.api_gateway.api_execution_arn}/*/*"
}

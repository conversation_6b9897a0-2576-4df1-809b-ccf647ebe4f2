# Azure API Management Outputs

output "api_management_id" {
  description = "ID of the API Management service"
  value       = azurerm_api_management.this.id
}

output "api_management_name" {
  description = "Name of the API Management service"
  value       = azurerm_api_management.this.name
}

output "gateway_url" {
  description = "Gateway URL of the API Management service"
  value       = azurerm_api_management.this.gateway_url
}

output "management_api_url" {
  description = "Management API URL of the API Management service"
  value       = azurerm_api_management.this.management_api_url
}

output "portal_url" {
  description = "Portal URL of the API Management service"
  value       = azurerm_api_management.this.portal_url
}

output "developer_portal_url" {
  description = "Developer portal URL of the API Management service"
  value       = azurerm_api_management.this.developer_portal_url
}

output "public_ip_addresses" {
  description = "Public IP addresses of the API Management service"
  value       = azurerm_api_management.this.public_ip_addresses
}

output "private_ip_addresses" {
  description = "Private IP addresses of the API Management service"
  value       = azurerm_api_management.this.private_ip_addresses
}

output "identity" {
  description = "Identity block of the API Management service"
  value       = azurerm_api_management.this.identity
}

output "resource_group_name" {
  description = "Name of the resource group"
  value       = azurerm_api_management.this.resource_group_name
}

output "api_ids" {
  description = "Map of API names to their IDs"
  value = {
    for k, v in azurerm_api_management_api.this : k => v.id
  }
}

output "product_ids" {
  description = "Map of product names to their IDs"
  value = {
    for k, v in azurerm_api_management_product.this : k => v.id
  }
}

output "api_urls" {
  description = "Map of API names to their full URLs"
  value = {
    for k, v in azurerm_api_management_api.this : k => "${azurerm_api_management.this.gateway_url}${v.path}"
  }
}

# Outputs for Simple Lambda API Example

output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = module.api_gateway.invoke_url
}

output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = module.api_gateway.api_id
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.hello_world.function_name
}

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.hello_world.arn
}

output "test_urls" {
  description = "URLs to test the API"
  value = {
    get_hello  = "${module.api_gateway.invoke_url}/hello"
    post_hello = "${module.api_gateway.invoke_url}/hello"
  }
}
